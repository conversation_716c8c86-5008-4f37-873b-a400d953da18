# -*- coding: utf-8 -*-
"""Copy of PyPOTS_Quick_Start.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1xAlxYrEn5JS_M-jTO17gIy27OLZRvCvx

# <a href="https://github.com/WenjieDu/PyPOTS"><img src="https://pypots.com/figs/pypots_logos/PyPOTS/logo_F5BG.svg" alt="" aria-label="logo" height="60" align='center'></a>Quick-start Tutorial for [PyPOTS](https://github.com/WenjieDu/PyPOTS) is Here!

#### 🤗 Hello there, in this quick-start tutorial, we show you how to run deep learning models in PyPOTS that is really simple ;~) PyPOTS supports five common anslysis tasks (imputation, forecasting, classification, clustering, and anomaly detection) on time series even your data contains missing values. For each analysis task, we select two algorithoms from PyPOTS. Surely you can try other models in PyPOTS (refer to [the model table here](https://github.com/WenjieDu/PyPOTS/?tab=readme-ov-file#-available-algorithms) where **50+** algorithms listed).

#### Enough for introduction. Let's get hands on it!

## Dependency Installation
"""

import pypots
import os

os.environ["HTTP_PROXY"] = "http://10.237.81.130:8080"
os.environ["HTTPS_PROXY"] = "http://10.237.81.130:8080"

"""## 📀 Preparing the **PhysioNet-2012** dataset for this tutorial"""

import numpy as np
import benchpots
from pypots.utils.random import set_random_seed

set_random_seed()

# Load the PhysioNet-2012 dataset
physionet2012_dataset = benchpots.datasets.preprocess_physionet2012(
    subset="set-a",
    rate=0.1,  # the rate of missing values artificially created to evaluate algorithms
)

# Take a look at the generated PhysioNet-2012 dataset, you'll find that everything has been prepared for you,
# data splitting, normalization, additional artificially-missing values for evaluation, etc.
print(physionet2012_dataset.keys())

"""## 🌟 Imputation Models

### 💿 Assembel the dateset for the imputation task
"""

# assemble the datasets for training
dataset_for_IMPU_training = {
    "X": physionet2012_dataset['train_X'],
}
# assemble the datasets for validation
dataset_for_IMPU_validating = {
    "X": physionet2012_dataset['val_X'],
    "X_ori": physionet2012_dataset['val_X_ori'],
}
# assemble the datasets for test
dataset_for_IMPU_testing = {
    "X": physionet2012_dataset['test_X'],
}
## calculate the mask to indicate the ground truth positions in test_X_ori, will be used by metric funcs to evaluate models
test_X_indicating_mask = np.isnan(physionet2012_dataset['test_X_ori']) ^ np.isnan(physionet2012_dataset['test_X'])
test_X_ori = np.nan_to_num(physionet2012_dataset['test_X_ori'])  # metric functions do not accpet input with NaNs, hence fill NaNs with 0

"""### 🚀 An example of [**SAITS**](https://arxiv.org/abs/2202.08516) for imputation"""

from pypots.nn.functional import calc_mae
from pypots.optim import Adam
from pypots.imputation import SAITS

# initialize the model
saits = SAITS(
    n_steps=physionet2012_dataset['n_steps'],
    n_features=physionet2012_dataset['n_features'],
    n_layers=1,
    d_model=256,
    d_ffn=128,
    n_heads=4,
    d_k=64,
    d_v=64,
    dropout=0.1,
    ORT_weight=1,  # you can adjust the weight values of arguments ORT_weight
    # and MIT_weight to make the SAITS model focus more on one task. Usually you can just leave them to the default values, i.e. 1.
    MIT_weight=1,
    batch_size=32,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/imputation/saits",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
saits.fit(
    train_set = dataset_for_IMPU_training,
    val_set=dataset_for_IMPU_validating
)

# the testing stage, impute the originally-missing values and artificially-missing values in the test set
saits_results = saits.predict(dataset_for_IMPU_testing)
saits_imputation = saits_results["imputation"]

# calculate mean absolute error on the ground truth (artificially-missing values)
testing_mae = calc_mae(
    saits_imputation,
    test_X_ori,
    test_X_indicating_mask,
)
print(f"Testing mean absolute error: {testing_mae:.4f}")

"""### 🚀 An example of [**CSDI**](https://arxiv.org/abs/2107.03502) for imputation"""

from pypots.optim import Adam
from pypots.imputation import CSDI
from pypots.nn.functional import calc_mae

# initialize the model
csdi = CSDI(
    n_steps=physionet2012_dataset['n_steps'],
    n_features=physionet2012_dataset['n_features'],
    n_layers=3,
    n_heads=2,
    n_channels=128,
    d_time_embedding=64,
    d_feature_embedding=32,
    d_diffusion_embedding=128,
    target_strategy="random",
    n_diffusion_steps=50,
    batch_size=32,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/imputation/csdi",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)


# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
csdi.fit(
    train_set = dataset_for_IMPU_training,
    val_set = dataset_for_IMPU_validating,
)

# the testing stage, impute the originally-missing values and artificially-missing values in the test set
csdi_results = csdi.predict(
    dataset_for_IMPU_testing,
    n_sampling_times = 2,  # CSDI has an argument to control the number of sampling times during inference
)
csdi_imputation = csdi_results["imputation"]

print(f"The shape of csdi_imputation is {csdi_imputation.shape}")

# for error calculation, we need to take the mean value of the multiple samplings for each data sample
mean_csdi_imputation = csdi_imputation.mean(axis=1)

# calculate mean absolute error on the ground truth (artificially-missing values)
testing_mae = calc_mae(
    mean_csdi_imputation,
    test_X_ori,
    test_X_indicating_mask,
)
print(f"Testing mean absolute error: {testing_mae:.4f}")

"""## 🌟 Forecasting Models

### 💿 Assemble the dateset for the forecasting task
"""

# Assemble the datasets for training, validating, and testing.

N_PRED_STEPS = 6

dataset_for_FORE_training = {
    "X": physionet2012_dataset['train_X'][:, :-N_PRED_STEPS],
    "X_pred": physionet2012_dataset['train_X'][:, -N_PRED_STEPS:],
}

dataset_for_FORE_validating = {
    "X": physionet2012_dataset['val_X'][:, :-N_PRED_STEPS],
    "X_pred": physionet2012_dataset['val_X_ori'][:, -N_PRED_STEPS:],
}

dataset_for_FORE_testing = {
    "X": physionet2012_dataset['test_X'][:, :-N_PRED_STEPS],  # we only take the first 42 steps for model input,
    # and let the model forecast the left 6 steps
}

"""### 🚀 An example of [**TEFN**](https://arxiv.org/abs/2405.06419) for forecasting"""

from pypots.optim import Adam
from pypots.forecasting import TEFN
from pypots.nn.functional import calc_mae

# initialize the model
tefn = TEFN(
    n_steps = physionet2012_dataset["n_steps"] - N_PRED_STEPS,
    n_features = physionet2012_dataset["n_features"],
    n_pred_steps = N_PRED_STEPS,
    n_pred_features = physionet2012_dataset["n_features"],
    n_fod = 2,
    batch_size=32,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/forecasting/tefn",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
tefn.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)
# BTTF does not need to run func fits().

# the testing stage
tefn_results = tefn.predict(dataset_for_FORE_testing)
tefn_prediction = tefn_results["forecasting"]

# calculate the mean absolute error on the ground truth in the forecasting task
testing_mae = calc_mae(
    tefn_prediction,
    np.nan_to_num(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:]),
    (~np.isnan(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:])).astype(int),
)
print(f"Testing mean absolute error: {testing_mae:.4f}")

"""### 🚀 An example of [**TimeMixer++**](https://arxiv.org/abs/2410.16032) for forecasting"""

from pypots.optim import Adam
from pypots.forecasting import TimeMixer
from pypots.nn.functional import calc_mae

timemixer = TimeMixer(
    n_steps = physionet2012_dataset["n_steps"] - N_PRED_STEPS,
    n_features = physionet2012_dataset["n_features"],
    n_pred_steps = N_PRED_STEPS,
    n_pred_features = physionet2012_dataset["n_features"],
    term = "short",
    n_layers=2,
    top_k=5,
    d_model=32,
    d_ffn=32,
    moving_avg=25,
    downsampling_window=2,
    downsampling_layers=1,
    use_norm=True,
    dropout=0.1,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/forecasting/timemixer",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
timemixer.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)
# BTTF does not need to run func fits().

# the testing stage
timemixer_results = timemixer.predict(dataset_for_FORE_testing)
timemixer_prediction = timemixer_results["forecasting"]

# calculate the mean absolute error on the ground truth in the forecasting task
testing_mae = calc_mae(
    timemixer_prediction,
    np.nan_to_num(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:]),
    (~np.isnan(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:])).astype(int),
)
print(f"Testing mean absolute error: {testing_mae:.4f}")

"""## 🌟 Classification Models

### 💿 Assemble the dateset for the classficiation task
"""

# Assemble the datasets for training, validating, and testing.

dataset_for_CLAS_training = {
    "X": physionet2012_dataset['train_X'],
    "y": physionet2012_dataset['train_y'],
}

dataset_for_CLAS_validating = {
    "X": physionet2012_dataset['val_X'],
    "y": physionet2012_dataset['val_y'],
}

dataset_for_CLAS_testing = {
    "X": physionet2012_dataset['test_X'],
    "y": physionet2012_dataset['test_y'],
}

"""### 🚀 An example of [**TimesNet**](https://arxiv.org/abs/2210.02186) for classification"""

from pypots.optim import Adam
from pypots.classification import TimesNet
from pypots.utils.metrics import calc_binary_classification_metrics

# initialize the model
timesnet = TimesNet(
    n_steps=physionet2012_dataset['n_steps'],
    n_features=physionet2012_dataset['n_features'],
    n_classes=physionet2012_dataset["n_classes"],
    n_layers=2,
    top_k=3,
    d_model=32,
    d_ffn=32,
    n_kernels=3,
    dropout=0.1,
    batch_size=32,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/classification/timesnet",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
timesnet.fit(
    train_set = dataset_for_CLAS_training,
    val_set = dataset_for_CLAS_validating,
)

# the testing stage
timesnet_results = timesnet.predict(dataset_for_CLAS_testing)
timesnet_prediction = timesnet_results["classification"]

# calculate the values of binary classification metrics on the model's prediction
metrics = calc_binary_classification_metrics(timesnet_prediction, dataset_for_CLAS_testing["y"])
print("Testing classification metrics: \n"
    f'ROC_AUC: {metrics["roc_auc"]}, \n'
    f'PR_AUC: {metrics["pr_auc"]},\n'
    f'F1: {metrics["f1"]},\n'
    f'Precision: {metrics["precision"]},\n'
    f'Recall: {metrics["recall"]},\n'
)

"""### 🚀 An example of [**BRITS**](https://arxiv.org/abs/1805.10572) for classification"""

from pypots.optim import Adam
from pypots.classification import BRITS
from pypots.utils.metrics import calc_binary_classification_metrics

# initialize the model
brits = BRITS(
    n_steps=physionet2012_dataset['n_steps'],
    n_features=physionet2012_dataset['n_features'],
    n_classes=physionet2012_dataset["n_classes"],
    rnn_hidden_size=256,
    batch_size=32,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/classification/brits",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
brits.fit(
    train_set = dataset_for_CLAS_training,
    val_set = dataset_for_CLAS_validating,
)

# the testing stage
brits_results = brits.predict(dataset_for_CLAS_testing)
brits_prediction = brits_results["classification"]

# calculate the values of binary classification metrics on the model's prediction
metrics = calc_binary_classification_metrics(brits_prediction, dataset_for_CLAS_testing["y"])
print("Testing classification metrics: \n"
    f'ROC_AUC: {metrics["roc_auc"]}, \n'
    f'PR_AUC: {metrics["pr_auc"]},\n'
    f'F1: {metrics["f1"]},\n'
    f'Precision: {metrics["precision"]},\n'
    f'Recall: {metrics["recall"]},\n'
)

"""## 🌟 Clustering Models

### 💿 Assemble the dateset for the clustering task
"""

# Assemble the datasets for training, validating, and testing.
import numpy as np

# don't need validation set
dataset_for_CLUS_training = {
    "X": np.concatenate([physionet2012_dataset['train_X'], physionet2012_dataset['val_X']], axis=0),
    "y": np.concatenate([physionet2012_dataset['train_y'], physionet2012_dataset['val_y']], axis=0),
}

dataset_for_CLUS_testing = {
    "X": physionet2012_dataset['test_X'],
    "y": physionet2012_dataset['test_y'],
}

"""### 🚀 An example of [**CRLI**](https://ojs.aaai.org/index.php/AAAI/article/view/17070) for clustering"""

from pypots.optim import Adam
from pypots.clustering import CRLI
from pypots.utils.metrics import calc_rand_index, calc_cluster_purity

# initialize the model
crli = CRLI(
    n_steps=physionet2012_dataset["n_steps"],
    n_features=physionet2012_dataset["n_features"],
    n_clusters=physionet2012_dataset["n_classes"],
    n_generator_layers=2,
    rnn_hidden_size=256,
    rnn_cell_type="GRU",
    decoder_fcn_output_dims=[256, 128],  # the output dimensions of layers in the decoder FCN.
    # Here means there are 3 layers. Leave it to default as None will results in
    # the FCN haveing only one layer.
    batch_size=32,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    G_optimizer=Adam(lr=1e-3),
    D_optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/clustering/crli",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
crli.fit(train_set=dataset_for_CLUS_training)

# the testing stage
crli_results = crli.predict(dataset_for_CLUS_testing)
crli_prediction = crli_results["clustering"]

# calculate the values of clustering metrics on the model's prediction
RI = calc_rand_index(crli_prediction, dataset_for_CLUS_testing["y"])
CP = calc_cluster_purity(crli_prediction, dataset_for_CLUS_testing["y"])

print("Testing clustering metrics: \n"
      f'RI: {RI}, \n'
      f'CP: {CP}\n'
)

"""### 🚀 An example of [**VaDER**](https://academic.oup.com/gigascience/article/8/11/giz134/5626377) for clustering"""

from pypots.optim import Adam
from pypots.clustering import VaDER
from pypots.utils.metrics import calc_rand_index, calc_cluster_purity

# initialize the model
vader = VaDER(
    n_steps=physionet2012_dataset["n_steps"],
    n_features=physionet2012_dataset["n_features"],
    n_clusters=physionet2012_dataset["n_classes"],
    rnn_hidden_size=128,
    d_mu_stddev=2,
    pretrain_epochs=20,
    batch_size=32,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/clustering/vader",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
vader.fit(train_set=dataset_for_CLUS_training)

# the testing stage
vader_results = vader.predict(dataset_for_CLUS_testing)
vader_prediction = vader_results["clustering"]

# calculate the values of clustering metrics on the model's prediction
RI = calc_rand_index(vader_prediction, dataset_for_CLUS_testing["y"])
CP = calc_cluster_purity(vader_prediction, dataset_for_CLUS_testing["y"])

print("Testing clustering metrics: \n"
      f'RI: {RI}, \n'
      f'CP: {CP},\n'
)

"""## 🌟 Anomaly Detection Models

### 📀 Preparing a synthetic dataset for anomaly detection

#### 🤔 Due to physionet-2012 does not have anomaly detection groud-truth labels in test set for model evalution, we generate a random walk dataset with anomalies here
"""

from benchpots.datasets import preprocess_random_walk

N_STEPS = 6
N_PRED_STEPS = 2
N_FEATURES = 5
ANOMALY_RATE = 0.05
MISSING_RATE = 0.1

ANDO_dataset = preprocess_random_walk(
    n_steps=N_STEPS + N_PRED_STEPS,  # the total sequence length
    n_features=N_FEATURES,
    anomaly_rate=ANOMALY_RATE,
    missing_rate=MISSING_RATE,
)

"""### 💿 Assemble the dateset for the anomaly detection task"""

# Assemble the datasets for training, validating, and testing.

dataset_for_ANOD_training = {
    "X": ANDO_dataset['train_X'],
    "anomaly_y": ANDO_dataset["train_anomaly_y"].astype(float),
}

dataset_for_ANOD_validating = {
    "X": ANDO_dataset['val_X'],
    "X_ori": ANDO_dataset["val_X_ori"],
    "anomaly_y": ANDO_dataset["val_anomaly_y"].astype(float),
}

dataset_for_ANOD_testing = {
    "X": ANDO_dataset['test_X'],
    "X_ori": ANDO_dataset["test_X_ori"],
    "anomaly_y": ANDO_dataset["test_anomaly_y"].astype(float), #
}

"""### 🚀 An example of [**Autoformer**](https://arxiv.org/abs/2106.13008) for anomaly detection"""

from pypots.optim import Adam
from pypots.anomaly_detection import Autoformer
from pypots.nn.functional import calc_acc, calc_precision_recall_f1

autoformer = Autoformer(
    n_steps = ANDO_dataset["n_steps"],
    n_features = ANDO_dataset["n_features"],
    anomaly_rate = ANOMALY_RATE,
    n_layers=2,
    n_heads=2,
    d_model=32,
    d_ffn=32,
    factor=3,
    moving_avg_window_size=3,
    dropout=0,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/anomaly_detection/autoformer",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

autoformer.fit(dataset_for_ANOD_training, dataset_for_ANOD_validating)

anomaly_detection_results = autoformer.predict(dataset_for_ANOD_testing)
anomaly_labels = dataset_for_ANOD_testing["anomaly_y"].flatten()
accuracy = calc_acc(
    anomaly_detection_results["anomaly_detection"],
    anomaly_labels,
)
precision, recall, f1 = calc_precision_recall_f1(
    anomaly_detection_results["anomaly_detection"],
    anomaly_labels,
)

print(f"Autoformer Accuracy: {accuracy}, F1: {f1}, Precision: {precision}, Recall: {recall}")

"""### 🚀 An example of [**PatchTST**](https://arxiv.org/abs/2211.14730) for anomaly detection"""

from pypots.optim import Adam
from pypots.anomaly_detection import PatchTST
from pypots.nn.functional import calc_acc, calc_precision_recall_f1

patchtst = PatchTST(
    n_steps = ANDO_dataset["n_steps"],
    n_features = ANDO_dataset["n_features"],
    anomaly_rate = ANOMALY_RATE,
    n_layers=2,
    d_model=64,
    n_heads=2,
    d_k=16,
    d_v=16,
    d_ffn=32,
    patch_size=ANDO_dataset["n_steps"],
    patch_stride=8,
    dropout=0.1,
    attn_dropout=0,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=Adam(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/anomaly_detection/patchtst",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

patchtst.fit(dataset_for_ANOD_training, dataset_for_ANOD_validating)

anomaly_detection_results = patchtst.predict(dataset_for_ANOD_testing)
anomaly_labels = dataset_for_ANOD_testing["anomaly_y"].flatten()
accuracy = calc_acc(
    anomaly_detection_results["anomaly_detection"],
    anomaly_labels,
)
precision, recall, f1 = calc_precision_recall_f1(
    anomaly_detection_results["anomaly_detection"],
    anomaly_labels,
)

print(f"PatchTST Accuracy: {accuracy}, F1: {f1}, Precision: {precision}, Recall: {recall}")
